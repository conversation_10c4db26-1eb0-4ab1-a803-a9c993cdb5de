package handlers

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/coolbox13/server_go/internal/auth"
	"github.com/coolbox13/server_go/internal/interfaces"
	"github.com/coolbox13/server_go/internal/logging"
	"github.com/coolbox13/server_go/internal/models"
	"github.com/gorilla/mux"
)

// AuthHandler handles HTTP requests related to user authentication.
type AuthHandler struct {
	*BaseHandler
	UserService    interfaces.UserServiceInterface
	SessionManager *auth.SessionManager
	// Add other services like ClerkService if needed
}

// AuthHandlerFactory is a function type for creating AuthHandler instances.
type AuthHandlerFactory func(base *BaseHandler, userService interfaces.UserServiceInterface, sessionManager *auth.SessionManager) *AuthHandler

// NewAuthHandler creates a new AuthHandler.
func NewAuthHandler(base *BaseHandler, userService interfaces.UserServiceInterface, sessionManager *auth.SessionManager) *AuthHand<PERSON> {
	return &AuthHandler{
		BaseHandler:    base,
		UserService:    userService,
		SessionManager: sessionManager,
	}
}

// HandleLoginPage blocks direct access and redirects to main app with auth state
func (ah *AuthHandler) HandleLoginPage(w http.ResponseWriter, r *http.Request) {
	// Block direct URL access - redirect to main app with auth state
	http.Redirect(w, r, "/?auth=login", http.StatusSeeOther)
}

// HandleSignupPage blocks direct access and redirects to main app with auth state
func (ah *AuthHandler) HandleSignupPage(w http.ResponseWriter, r *http.Request) {
	// Block direct URL access - redirect to main app with auth state
	http.Redirect(w, r, "/?auth=signup", http.StatusSeeOther)
}

// HandleSignup handles the submission of the signup form.
func (ah *AuthHandler) HandleSignup(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		ah.handleError(w, r, "Method not allowed", errors.New("expected POST for signup"), http.StatusMethodNotAllowed)
		return
	}

	// Parse form data
	if err := r.ParseForm(); err != nil {
		ah.handleError(w, r, "Error parsing signup form", err, http.StatusBadRequest)
		return
	}

	// Validate CSRF token
	csrfToken := r.FormValue("csrf_token")
	if csrfToken == "" {
		data := ah.NewBasePageData(r, "Sign Up - Omfietser", models.User{}, []interface{}{"Security token missing. Please refresh and try again."})
		data["FormEmail"] = r.FormValue("email")
		ah.RenderPage(w, r, "pages/signup.html", data)
		return
	}

	email := r.FormValue("email")
	password := r.FormValue("password")
	// confirmPassword := r.FormValue("confirm_password") // Optional: add password confirmation

	// Basic validation - only email and password required
	if email == "" || password == "" {
		// For HTMX requests, return just the error message
		if r.Header.Get("HX-Request") == "true" {
			w.Header().Set("Content-Type", "text/html")
			w.Write([]byte(`<div id="error-messages" class="auth-error-messages">
				<p>Email and password are required.</p>
			</div>`))
			return
		}
		// TODO: Add flash message for error
		data := ah.NewBasePageData(r, "Sign Up - Omfietser", models.User{}, []interface{}{"Email and password are required."})
		data["FormEmail"] = email // Repopulate form
		ah.RenderPage(w, r, "pages/signup.html", data)
		return
	}

	// Call UserService to register the user (email-only authentication)
	newUser, err := ah.UserService.RegisterUser(r.Context(), email, password)
	if err != nil {
		// Handle specific errors like "email already exists"
		// TODO: Add flash message for error
		errorMessage := "Failed to register user. Please try again."
		switch err.Error() { // This is a bit brittle; ideally use custom error types
		case "email already exists":
			errorMessage = "This email address is already registered."
		default:
			logging.L().Error("Error registering user in HandleSignup", "error", err)
		}

		// For HTMX requests, return just the error message
		if r.Header.Get("HX-Request") == "true" {
			w.Header().Set("Content-Type", "text/html")
			w.Write([]byte(fmt.Sprintf(`<div id="error-messages" class="auth-error-messages">
				<p>%s</p>
			</div>`, errorMessage)))
			return
		}

		data := ah.NewBasePageData(r, "Sign Up - Omfietser", models.User{}, []interface{}{errorMessage})
		data["FormEmail"] = email
		ah.RenderPage(w, r, "pages/signup.html", data)
		return
	}

	// Registration successful, create session
	sessionID, _, expiresAt, err := ah.SessionManager.CreateSession(r.Context(), newUser.ID)
	if err != nil {
		ah.handleError(w, r, "Error creating session after signup", err, http.StatusInternalServerError)
		return
	}

	// Set session cookie
	ah.SessionManager.SetSessionCookie(w, sessionID, expiresAt)

	// TODO: Add flash message for successful registration

	// Handle HTMX requests differently - use HX-Redirect header
	if r.Header.Get("HX-Request") == "true" {
		w.Header().Set("HX-Redirect", "/")
		w.WriteHeader(http.StatusOK)
		logging.L().Info("User registered successfully via HTMX", "userID", newUser.ID, "email", newUser.Email)
		return
	}

	// Redirect to home page or a welcome page
	http.Redirect(w, r, "/", http.StatusSeeOther)
	logging.L().Info("User registered successfully", "userID", newUser.ID, "email", newUser.Email)
}

// HandleLogin handles the submission of the login form.
func (ah *AuthHandler) HandleLogin(w http.ResponseWriter, r *http.Request) {
	logging.L().Debug("HandleLogin called", "method", r.Method, "path", r.URL.Path)

	if r.Method != http.MethodPost {
		ah.handleError(w, r, "Method not allowed", errors.New("expected POST for login"), http.StatusMethodNotAllowed)
		return
	}

	// Parse form data
	if err := r.ParseForm(); err != nil {
		ah.handleError(w, r, "Error parsing login form", err, http.StatusBadRequest)
		return
	}

	// TEMPORARILY DISABLED: Validate CSRF token
	// csrfToken := r.FormValue("csrf_token")
	// if csrfToken == "" {
	// 	// For HTMX requests, return just the error message
	// 	if r.Header.Get("HX-Request") == "true" {
	// 		w.Header().Set("Content-Type", "text/html")
	// 		w.Write([]byte(`<div id="error-messages" class="mb-4 p-4 bg-red-100 text-red-700 rounded-md">
	// 			<p>Security token missing. Please refresh and try again.</p>
	// 		</div>`))
	// 		return
	// 	}
	// 	data := ah.NewBasePageData(r, "Login - Omfietser", models.User{}, []interface{}{"Security token missing. Please refresh and try again."})
	// 	data["FormEmail"] = r.FormValue("email")
	// 	ah.RenderPage(w, r, "pages/login.html", data)
	// 	return
	// }

	email := r.FormValue("email")
	password := r.FormValue("password")

	logging.L().Debug("Login attempt", "email", email, "password_length", len(password))

	// Basic validation
	if email == "" || password == "" {
		// For HTMX requests, return just the error message
		if r.Header.Get("HX-Request") == "true" {
			w.Header().Set("Content-Type", "text/html")
			w.Write([]byte(`<div id="error-messages" class="auth-error-messages">
				<p>Email and password are required.</p>
			</div>`))
			return
		}
		data := ah.NewBasePageData(r, "Login - Omfietser", models.User{}, []interface{}{"Email and password are required."})
		data["FormEmail"] = email // Repopulate form
		ah.RenderPage(w, r, "pages/login.html", data)
		return
	}

	// Call UserService to authenticate the user
	logging.L().Debug("Calling AuthenticateUser", "email", email)
	authenticatedUser, err := ah.UserService.AuthenticateUser(r.Context(), email, password)
	if err != nil {
		logging.L().Debug("Authentication failed", "email", email, "error", err)
		// Handle authentication failure (e.g., invalid credentials, user not found)
		errorMessage := "Invalid email or password."
		if err.Error() != "invalid email/username or password" { // Log if it's not the expected auth error
			logging.L().Error("Error authenticating user in HandleLogin", "error", err)
			errorMessage = "An error occurred. Please try again."
		}

		// For HTMX requests, return just the error message
		if r.Header.Get("HX-Request") == "true" {
			w.Header().Set("Content-Type", "text/html")
			w.Write([]byte(fmt.Sprintf(`<div id="error-messages" class="auth-error-messages">
				<p>%s</p>
			</div>`, errorMessage)))
			return
		}

		data := ah.NewBasePageData(r, "Login - Omfietser", models.User{}, []interface{}{errorMessage})
		data["FormEmail"] = email
		ah.RenderPage(w, r, "pages/login.html", data)
		return
	}

	// Authentication successful, create session
	logging.L().Debug("Authentication successful", "email", email, "user_id", authenticatedUser.ID)
	sessionID, csrfToken, expiresAt, err := ah.SessionManager.CreateSession(r.Context(), authenticatedUser.ID)
	if err != nil {
		ah.handleError(w, r, "Error creating session after login", err, http.StatusInternalServerError)
		return
	}

	// Set session cookie
	ah.SessionManager.SetSessionCookie(w, sessionID, expiresAt)

	// Optionally, set CSRF token in a header if client-side JS needs it directly after login.
	// Or rely on it being in forms rendered by the server.
	w.Header().Set(auth.CSRFHeaderName, csrfToken) // Example if needed by HTMX immediately

	// TODO: Add flash message for successful login

	// Update last login time (can also be done in AuthenticateUser)
	if err := ah.UserService.UpdateLastLogin(r.Context(), authenticatedUser.ID); err != nil {
		logging.L().Warn("Failed to update last login time", "userID", authenticatedUser.ID, "error", err)
		// Non-critical error, proceed with login
	}

	// Handle HTMX requests differently - use HX-Redirect header
	if r.Header.Get("HX-Request") == "true" {
		w.Header().Set("HX-Redirect", "/")
		w.WriteHeader(http.StatusOK)
		logging.L().Info("User logged in successfully via HTMX", "userID", authenticatedUser.ID, "email", authenticatedUser.Email)
		return
	}

	// Redirect to home page or a target page (e.g., if ?next=/some/path was present)
	http.Redirect(w, r, "/", http.StatusSeeOther)
	logging.L().Info("User logged in successfully", "userID", authenticatedUser.ID, "email", authenticatedUser.Email)
}

// HandleLogout performs user logout.
// It retrieves the session ID from the cookie, deletes the session from Redis,
// clears the session cookie, and redirects the user.
func (ah *AuthHandler) HandleLogout(w http.ResponseWriter, r *http.Request) {
	// It's good practice to ensure logout is a POST request to prevent CSRF issues with GET-based logout.
	// However, sometimes GET logout is used for simplicity. For now, allowing GET/POST.
	// if r.Method != http.MethodPost {
	// 	ah.handleError(w, r, "Method not allowed for logout", errors.New("expected POST for logout"), http.StatusMethodNotAllowed)
	// 	return
	// }

	cookie, err := r.Cookie(auth.SessionCookieName) // Use constant from auth package
	if err == nil && cookie != nil && cookie.Value != "" {
		sessionID := cookie.Value
		if err := ah.SessionManager.DeleteSession(r.Context(), sessionID); err != nil {
			// Log error but proceed with clearing cookie and redirecting as a best effort.
			logging.L().Error("Error deleting session from Redis during logout", "sessionID", sessionID, "error", err)
		}
	} else if err != http.ErrNoCookie {
		// Some other error occurred trying to read the cookie, log it.
		logging.L().Warn("Error reading session cookie during logout", "error", err)
	}

	// Clear the session cookie regardless of whether it was found or if Redis deletion succeeded.
	ah.SessionManager.ClearSessionCookie(w)

	// TODO: Add flash message for successful logout (e.g., "You have been logged out.")

	// Redirect to the homepage (PWA app-first approach - no direct login page access)
	http.Redirect(w, r, "/", http.StatusSeeOther)
	logging.L().Info("User logged out")
}

// RegisterRoutes sets up the routes for the AuthHandler.
func (ah *AuthHandler) RegisterRoutes(router *mux.Router) {
	router.HandleFunc("/login", ah.HandleLoginPage).Methods("GET")
	router.HandleFunc("/login", ah.HandleLogin).Methods("POST")
	router.HandleFunc("/signup", ah.HandleSignupPage).Methods("GET")
	router.HandleFunc("/signup", ah.HandleSignup).Methods("POST")
	router.HandleFunc("/logout", ah.HandleLogout).Methods("POST", "GET") // Allow GET for logout for now
}

// NewBasePageData creates the basic data structure for rendering a page.
func (ah *AuthHandler) NewBasePageData(r *http.Request, title string, currentUser models.User, flashes []interface{}) map[string]interface{} {
	// Get CSRF token from session if available
	csrfToken := ""
	cookie, err := r.Cookie(auth.SessionCookieName)
	if err == nil && cookie != nil && cookie.Value != "" {
		_, token, err := ah.SessionManager.GetSessionData(r.Context(), cookie.Value)
		if err == nil {
			csrfToken = token
		}
	}

	// If no session token, generate a new one for forms
	if csrfToken == "" {
		csrfToken, _ = auth.GenerateRandomString(auth.CSRFTokenLength)
	}

	// Determine content template name based on the request path
	contentTemplateName := ""
	switch r.URL.Path {
	case "/login":
		contentTemplateName = "login"
	case "/signup":
		contentTemplateName = "signup"
	}

	return map[string]interface{}{
		"Title":               title,
		"CSRFToken":           csrfToken,
		"CurrentUser":         currentUser,
		"AssetPath":           AssetPath,
		"Flashes":             flashes,
		"IsDevelopment":       ah.BaseHandler.cfg.Environment == "development",
		"ContentTemplateName": contentTemplateName,
	}
}

// RenderContentTemplate renders content-only templates using FrontendHandler's template collection
func (ah *AuthHandler) RenderContentTemplate(w http.ResponseWriter, r *http.Request, templateName string, data map[string]interface{}) error {
	// Get the FrontendHandler to access its templates
	if parent, ok := ah.BaseHandler.Parent.(*FrontendHandler); ok {
		// Ensure templates are loaded in development mode
		if ah.BaseHandler.cfg.Environment == "development" {
			if err := parent.loadTemplates(); err != nil {
				logging.L().Error("Failed to reload FrontendHandler templates", "error", err)
				return err
			}
		}

		// Use FrontendHandler's templates to render the content template
		tmpl := parent.templates.Lookup(templateName)
		if tmpl == nil {
			logging.L().Error("Content template not found in FrontendHandler", "templateName", templateName)
			return fmt.Errorf("content template %s not found", templateName)
		}

		return tmpl.Execute(w, data)
	}

	return fmt.Errorf("AuthHandler parent is not a FrontendHandler")
}

// RenderPage executes the specified template with the given data using hybrid template approach
func (ah *AuthHandler) RenderPage(w http.ResponseWriter, r *http.Request, templateName string, data map[string]interface{}) error {
	// For base template, we need to use FrontendHandler's templates which include content templates
	if templateName == "base" {
		if parent, ok := ah.BaseHandler.Parent.(*FrontendHandler); ok {
			// Ensure templates are loaded in development mode
			if ah.BaseHandler.cfg.Environment == "development" {
				if err := parent.loadTemplates(); err != nil {
					logging.L().Error("Failed to reload FrontendHandler templates", "error", err)
					return err
				}
			}

			// Use FrontendHandler's templates which include both base and content templates
			tmpl := parent.templates.Lookup(templateName)
			if tmpl == nil {
				logging.L().Error("Base template not found in FrontendHandler", "templateName", templateName)
				return fmt.Errorf("base template %s not found", templateName)
			}

			return tmpl.Execute(w, data)
		}
		return fmt.Errorf("AuthHandler parent is not a FrontendHandler")
	}

	// For emergency pages, use BaseHandler's templates
	tmpl := ah.BaseHandler.templates.Lookup(templateName)
	if tmpl == nil {
		emergencyData := map[string]interface{}{
			"Title":         fmt.Sprintf("Error - Template Not Found (%s)", templateName),
			"ErrorMessage":  fmt.Sprintf("The template '%s' could not be found. Please contact support.", templateName),
			"IsDevelopment": ah.BaseHandler.cfg.Environment == "development",
			"AssetPath":     AssetPath,
			"CSRFToken":     ah.GetCSRFToken(r),
		}
		logging.L().Error("Template not found, attempting to render emergency error page", "templateName", templateName)
		if execErr := ah.BaseHandler.templates.ExecuteTemplate(w, "emergency_error.html", emergencyData); execErr != nil {
			logging.L().Error("Failed to render emergency error page", "error", execErr)
			http.Error(w, "A critical error occurred and the error page could not be displayed.", http.StatusInternalServerError)
		}
		return fmt.Errorf("template %s not found", templateName)
	}
	return tmpl.Execute(w, data)
}

// handleError logs an error and writes an HTTP error response.
func (ah *AuthHandler) handleError(w http.ResponseWriter, r *http.Request, message string, err error, statusCode int) {
	logging.L().Error(message, "error", err, "path", r.URL.Path, "method", r.Method, "originalError", err.Error())

	if ah.BaseHandler.templates != nil {
		data := map[string]interface{}{
			"Title":         fmt.Sprintf("%d Error - %s", statusCode, http.StatusText(statusCode)),
			"ErrorMessage":  message,
			"StatusCode":    statusCode,
			"IsDevelopment": ah.BaseHandler.cfg.Environment == "development",
			"AssetPath":     AssetPath,
			"CSRFToken":     ah.GetCSRFToken(r),
		}
		templateToRender := "emergency_error.html"
		if statusCode == http.StatusNotFound {
			templateToRender = "emergency_404.html"
		} else if statusCode >= http.StatusInternalServerError {
			templateToRender = "emergency_500.html"
		}

		errPage := ah.BaseHandler.templates.Lookup(templateToRender)
		if errPage != nil {
			w.WriteHeader(statusCode)
			if renderErr := errPage.Execute(w, data); renderErr != nil {
				logging.L().Error("Failed to render custom error page", "template", templateToRender, "renderError", renderErr.Error())
			} else {
				return // Successfully rendered error page
			}
		} else {
			logging.L().Warn("Error page template not found", "template", templateToRender)
		}
	}

	http.Error(w, http.StatusText(statusCode), statusCode)
}

// reloadTemplatesIfNeeded reloads templates in development mode
func (ah *AuthHandler) reloadTemplatesIfNeeded() {
	if ah.BaseHandler.cfg.Environment == "development" {
		// Get the parent FrontendHandler to reload templates
		if parent, ok := ah.BaseHandler.Parent.(*FrontendHandler); ok {
			if err := parent.loadTemplates(); err != nil {
				logging.L().Error("Failed to reload templates in AuthHandler", "error", err)
			} else {
				// Update the BaseHandler's templates reference to use the FrontendHandler's templates
				ah.BaseHandler.templates = parent.templates
			}
		}
	}
}
