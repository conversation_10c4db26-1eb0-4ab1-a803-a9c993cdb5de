// Package handlers provides HTTP request handlers for the application.
package handlers

import (
	"context"
	"fmt"
	"log/slog"
	"net/http"

	"github.com/coolbox13/server_go/internal/logging"
	"github.com/coolbox13/server_go/internal/models"
	"github.com/gorilla/mux"
)

// HandleHome renders the home page, showing products or the home component depending on user state.
// Also handles authentication state via query parameters (?auth=login or ?auth=signup)
func (h *FrontendHandler) HandleHome(w http.ResponseWriter, r *http.Request) {
	log := logging.L()
	log.Info("+++++ HandleHome being executed! +++++")

	// Check CSRF token and get context
	h.checkCsrfToken(r)
	ctx := r.Context()

	// Reload templates in development mode
	h.reloadTemplatesIfNeeded()

	// Check for authentication state in query parameters
	authState := r.URL.Query().Get("auth")
	if authState == "login" || authState == "signup" {
		h.renderAuthenticationPage(w, r, authState)
		return
	}

	// Check if this is an HTMX request
	if h.IsHtmxRequest(r) {
		h.renderHomeComponentForHtmx(w)
		return
	}

	// Get configuration values
	buildDate, version := h.getConfigValues()

	// Fetch products and process user favorites
	products, total, err := h.fetchProducts(ctx)
	if err != nil {
		renderError(w, ErrInternalServer, http.StatusInternalServerError)
		return
	}

	// Get user and create product view models
	userInfo, productViewModels := h.processUserAndProducts(r.Context(), products)

	// Determine template to show
	contentTemplateName := h.determineContentTemplate()

	// Prepare template data
	data := h.prepareTemplateData(contentTemplateName, productViewModels, total, userInfo, buildDate, version, r)

	// Render the template
	h.renderTemplateWithErrorHandling(w, TemplateBase, data)
}

// renderHomeComponentForHtmx renders the home component for HTMX requests
func (h *FrontendHandler) renderHomeComponentForHtmx(w http.ResponseWriter) {
	h.renderTemplateWithErrorHandling(w, TemplateHomeComponent, nil)
}

// renderAuthenticationPage renders authentication content based on auth state
// For HTMX requests: renders content-only template for main area swapping
// For direct requests: renders full page with base layout
func (h *FrontendHandler) renderAuthenticationPage(w http.ResponseWriter, r *http.Request, authState string) {
	log := logging.L()
	log.Info("Rendering authentication page", "auth_state", authState, "is_htmx", h.IsHtmxRequest(r))

	// Determine content template based on auth state
	var contentTemplateName string
	var title string
	switch authState {
	case "login":
		contentTemplateName = "login-content"
		title = "Login - Omfietser"
	case "signup":
		contentTemplateName = "signup-content"
		title = "Sign Up - Omfietser"
	default:
		// Invalid auth state, redirect to home
		http.Redirect(w, r, "/", http.StatusSeeOther)
		return
	}

	// Prepare template data for authentication page
	data := map[string]interface{}{
		"Title":               title,
		"Description":         "Omfietser Authentication",
		"CurrentPage":         "auth",
		"ContentTemplateName": contentTemplateName,
		"CSRFToken":           h.GetCSRFToken(r),
		"Flashes":             []interface{}{}, // TODO: Integrate with session manager for flash messages
		"FormEmail":           "",              // Empty for initial load
	}

	// Add base template data (includes IsLoggedIn, asset paths, etc.)
	finalData := h.BaseHandler.AddBaseTemplateData(data, r)

	// Check if this is an HTMX request targeting main content
	if h.IsHtmxRequest(r) && r.Header.Get("HX-Target") == "main" {
		// Render content-only template for HTMX content swapping
		h.renderTemplateWithErrorHandling(w, contentTemplateName, finalData)
	} else {
		// Render full page with base layout for direct navigation
		h.renderTemplateWithErrorHandling(w, TemplateBase, finalData)
	}
}

// fetchProducts fetches products for the home page
func (h *FrontendHandler) fetchProducts(ctx context.Context) ([]*models.Product, int, error) {
	page := 1
	pageSize := 24

	products, total, err := h.ProductService.GetProducts(ctx, nil, page, pageSize)
	if err != nil {
		logging.L().Error(ErrFailedGetProducts, "error", err)
		return nil, 0, err
	}

	return products, total, nil
}

// determineContentTemplate determines which template to show based on user state
func (h *FrontendHandler) determineContentTemplate() string {
	if h.cfg.UserLoggedIn {
		return "" // Empty to show product-list for logged in users
	}
	return TemplateHomeComponent // Home component for non-logged in users
}

// prepareTemplateData prepares the template data map
func (h *FrontendHandler) prepareTemplateData(contentTemplateName string, productViewModels []ProductViewModel, total int, user *models.User, buildDate, version string, r *http.Request) map[string]interface{} {
	page := 1
	pageSize := 24

	// Create a new FilterState
	filterState := models.NewFilterState()
	filterState.Page = page
	filterState.PageSize = pageSize
	filterState.Sort = "price_desc" // Default sort

	// Calculate if there's a next page for home
	hasNextPage := total > 0 && (page*pageSize) < total

	// Data for the template - ONLY app-specific data, no base template conflicts
	data := map[string]interface{}{
		"Title":               "Home - Find the Best Deals",
		"Description":         "Compare supermarket prices across stores",
		"CurrentPage":         "home",
		"ContentTemplateName": contentTemplateName,
		"Products":            productViewModels,
		"Total":               total,
		"Page":                page,
		"PageSize":            pageSize,
		"NextPage":            page + 1,
		"HasMore":             hasNextPage,
		"Sort":                "price_desc", // Default sort
		"AvailableShops":      h.availableShops,
		"AvailableCategories": h.availableCategories,
		"SearchTerm":          "",
		"ShopFilter":          nil,
		"CategoryFilter":      nil,
		"PriceRangeFilter":    nil,
		"PromotionFilter":     nil,
		"FilterState":         filterState,
		"ShopTypes":           GetShopTypes(),
		"MainCategories":      GetMainCategories(),
		"PromotionTypes":      GetPromotionTypes(),
		"PriceRanges":         GetPriceRanges(),
		"SortOptions":         getSortOptions(),
		"NotificationCount":   0,
		"UserPreferences":     map[string]string{},
	}

	// Add user-specific data if available
	if user != nil {
		data["UserID"] = user.ID
		data["UserName"] = user.FirstName + " " + user.LastName
		data["UserEmail"] = user.Email
		data["UserProfilePicture"] = ""
	} else {
		data["UserID"] = 0
		data["UserName"] = ""
		data["UserEmail"] = ""
		data["UserProfilePicture"] = ""
	}

	// Let AddBaseTemplateData handle ALL base template data including assets, environment, user state
	finalData := h.BaseHandler.AddBaseTemplateData(data, r)

	// Log data for debugging
	h.logDebugData(finalData)

	return finalData
}

// HandleProducts renders the products page with a list of products or, for HTMX requests, just the product cards partial.
func (h *FrontendHandler) HandleProducts(w http.ResponseWriter, r *http.Request) {
	log := logging.L()
	ctx := r.Context()

	h.reloadTemplatesIfNeeded()

	// Get FilterState from request parameters
	filterState := h.BaseHandler.getFilterStateFromRequest(r)
	log.Info("FilterState processed from request", "filter_state", filterState)

	// Initialize basic page data
	pageData := h.initializeProductPageData(filterState)

	// Handle view-specific behaviors (e.g., favorites, cart)
	if filterState.ViewType != "" {
		if handleSpecialViewStates(w, r, h, filterState, pageData) {
			return
		}
	}

	// Fetch products and handle potential errors
	products, total, err := h.fetchProductsSorted(ctx, filterState)
	if err != nil {
		renderError(w, ErrInternalServer, http.StatusInternalServerError)
		return
	}

	logProductDetails(log, products)
	userInfo, productViewModels := h.processUserAndProducts(ctx, products)
	h.updatePageDataWithProducts(pageData, filterState, total, productViewModels, userInfo)

	finalData := h.BaseHandler.AddBaseTemplateData(pageData, r)

	// --- Unified rendering logic ---
	if r.Header.Get("HX-Request") == "true" {
		// HTMX request: render only product-cards template for true infinite scroll
		w.Header().Set(HeaderContentType, ContentTypeHTML)
		if !h.renderTemplateWithErrorHandling(w, "product-cards", finalData) {
			return
		}
		return
	}
	// Full page render
	h.renderHTMXOrFullPage(w, r, finalData, TemplateProductList)
}

// initializeProductPageData creates the initial page data structure
func (h *FrontendHandler) initializeProductPageData(filterState *models.FilterState) map[string]interface{} {
	return map[string]interface{}{
		"FilterState":         filterState,
		"SortOptions":         models.GetSortOptions(),
		"AvailableShops":      h.availableShops,
		"AvailableCategories": h.availableCategories,
		"UserMessage":         "",
		"Products":            []models.Product{},
		"Total":               0,
		"Page":                1, // Default, will be updated by filterState
		"PageSize":            filterState.PageSize,
		"NextPage":            0,
		"Title":               "Products",
		"Description":         "Browse all products",
		"CurrentPage":         "products",
	}
}

// handleSpecialViewStates manages cases like login-required views or empty views
// Returns true if the request was fully handled (response was sent)
func handleSpecialViewStates(w http.ResponseWriter, r *http.Request, h *FrontendHandler,
	filterState *models.FilterState, pageData map[string]interface{}) bool {

	log := logging.L()

	if filterState.ViewRequiresLogin {
		log.Info("View requires login, but user not authenticated.", "view_type", filterState.ViewType)
		pageData["UserMessage"] = fmt.Sprintf("Please log in to view your %s.", filterState.ViewType)

		finalData := h.BaseHandler.AddBaseTemplateData(pageData, r)
		h.renderHTMXOrFullPage(w, r, finalData, TemplateProductList)
		return true

	} else if filterState.ViewIsEmpty {
		log.Info("View is empty for user.", "view_type", filterState.ViewType)
		pageData["UserMessage"] = fmt.Sprintf("Your %s list is currently empty.", filterState.ViewType)

		finalData := h.BaseHandler.AddBaseTemplateData(pageData, r)
		h.renderHTMXOrFullPage(w, r, finalData, TemplateProductList)
		return true
	}

	return false
}

// fetchProductsSorted retrieves products using the service
func (h *FrontendHandler) fetchProductsSorted(ctx context.Context, filterState *models.FilterState) ([]*models.Product, int, error) {
	log := logging.L()

	// Convert filter state to database filters
	dbFilters := filterState.ToDBFilters()

	// Debug logging
	log.Debug("Fetching products with filters",
		"dbFilters", dbFilters,
		"search_term", filterState.SearchTerm,
		"sort", filterState.Sort,
		"page", filterState.Page,
		"pageSize", filterState.PageSize)

	// Fetch products using the service
	products, total, err := h.ProductService.GetProductsSorted(
		ctx,
		dbFilters,
		filterState.Sort,
		filterState.Page,
		filterState.PageSize,
	)

	if err != nil {
		log.Error(ErrFailedGetProducts, "error", err)
		return nil, 0, err
	}

	log.Debug("Products fetched successfully", "count", len(products), "total", total)
	return products, total, nil
}

// logProductDetails logs debug information about product image URLs
func logProductDetails(log *slog.Logger, products []*models.Product) {
	log.Debug("Verifying product image URLs")
	for i, p := range products {
		if i >= 3 { // Log only first 3
			break
		}
		log.Debug("Product Image URL", "index", i, "id", p.ID, "url", p.ImageURL)
	}
}

// updatePageDataWithProducts updates the page data with product information
func (h *FrontendHandler) updatePageDataWithProducts(
	pageData map[string]interface{},
	filterState *models.FilterState,
	total int,
	productViewModels []ProductViewModel,
	userInfo *models.User,
) {
	// Update with product data and pagination
	pageData["Products"] = productViewModels
	pageData["Total"] = total
	pageData["Page"] = filterState.Page
	pageData["User"] = userInfo

	// Calculate if there's a next page
	hasNextPage := total > 0 && (filterState.Page*filterState.PageSize) < total
	if hasNextPage {
		pageData["NextPage"] = filterState.Page + 1
		pageData["HasMore"] = true
	} else {
		pageData["NextPage"] = 0
		pageData["HasMore"] = false
	}
}

// HandleProductDetail renders the product detail page for a given product ID.
func (h *FrontendHandler) HandleProductDetail(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	productID := vars["id"]

	data := map[string]interface{}{
		"Title":                "Product Detail",
		"Description":          "View detailed product information",
		"CurrentPage":          "products", // Or perhaps "product-detail"
		"ProductID":            productID,
		"AvailableShops":       h.availableShops,
		"AvailableCategories":  h.availableCategories,
		"AvailableSortOptions": getSortOptions(),
	}

	// Add base template data
	finalData := h.BaseHandler.AddBaseTemplateData(data, r)

	if !h.renderTemplateWithErrorHandling(w, TemplateBase, finalData) {
		return
	}
}

// HandleDeals renders the deals page showing best deals across supermarkets.
func (h *FrontendHandler) HandleDeals(w http.ResponseWriter, r *http.Request) {
	data := map[string]interface{}{
		"Title":                "Best Deals",
		"Description":          "Find the best deals across different supermarkets",
		"CurrentPage":          "deals",
		"AvailableShops":       h.availableShops,
		"AvailableCategories":  h.availableCategories,
		"AvailableSortOptions": getSortOptions(),
	}

	// Add base template data
	finalData := h.BaseHandler.AddBaseTemplateData(data, r)

	if !h.renderTemplateWithErrorHandling(w, TemplateBase, finalData) {
		return
	}
}
