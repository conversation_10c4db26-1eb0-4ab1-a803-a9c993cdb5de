// Package user implements PostgreSQL repository functionality for user data.
// It provides methods for storing, retrieving, and manipulating user information.
package user

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/coolbox13/server_go/internal/database"
	"github.com/coolbox13/server_go/internal/logging"
	"github.com/coolbox13/server_go/internal/models"
	"github.com/coolbox13/server_go/internal/repository"
	"github.com/jackc/pgx/v5"
)

// Define error message constants for frequently used error messages
const (
	errFindUsers       = "failed to find users: %w"
	errScanUser        = "failed to scan user: %w"
	errProcessUserRows = "error processing user rows: %w"
)

// Repository implements repository.UserRepository for PostgreSQL.
// It provides database operations for user entities, including CRUD operations
// and managing related data such as favorite products, shopping lists, and preferences.
type Repository struct {
	db *database.Database
}

// New creates a new PostgreSQL user repository.
//
// Parameters:
//   - db: Database connection pool to use for queries
//
// Returns:
//   - repository.UserRepository: A repository implementation for user operations
func New(db *database.Database) repository.UserRepository {
	return &Repository{
		db: db,
	}
}

// Initialize implements Repository.Initialize.
// This method is a no-op for Repository as no initialization is needed.
//
// Parameters:
//   - ctx: Context for managing timeouts and cancellation
//
// Returns:
//   - error: Always returns nil as no initialization is required
func (r *Repository) Initialize(ctx context.Context) error {
	// No initialization needed
	return nil
}

// Close implements Repository.Close.
// This method is a no-op for Repository as no resources need to be closed.
//
// Parameters:
//   - ctx: Context for managing timeouts and cancellation
//
// Returns:
//   - error: Always returns nil as no resources need to be closed
func (r *Repository) Close(ctx context.Context) error {
	// No resources to close
	return nil
}

// Create implements UserRepository.Create.
// Creates a new user record in the database.
//
// Parameters:
//   - ctx: Context for managing timeouts and cancellation
//   - user: The user model to create
//
// Returns:
//   - error: Any error encountered during the operation
func (r *Repository) Create(ctx context.Context, user *models.User) error {
	// Prepare user for creation
	if err := user.BeforeCreate(); err != nil {
		return fmt.Errorf("failed to prepare user for creation: %w", err)
	}

	logging.L().Debug("Repository Create: Before inserting user",
		"email", user.Email,
		"passwordHashLength", len(user.PasswordHash),
		"passwordHashEmpty", user.PasswordHash == "",
		"role", user.Role)

	// Create user - Align with migration 0002 schema
	query := `
		INSERT INTO users (
			email, first_name, last_name, password_hash, role, is_active, is_subscribed,
			subscription_status, trial_start_date, trial_end_date, last_login, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
		) RETURNING id
	`
	// Get trial start/end from model (TrialEnd is mapped to trial_end_date)
	// Use the new TrialStartDate field from the model

	err := r.db.Pool.QueryRow(
		ctx, query,
		user.Email, user.FirstName, user.LastName, user.PasswordHash, user.Role,
		user.IsActive, user.IsSubscribed,
		user.SubscriptionStatus,
		user.TrialStartDate, // Use the new field
		user.TrialEnd,       // Mapped to trial_end_date
		user.LastLogin, user.CreatedAt, user.UpdatedAt,
	).Scan(&user.ID)

	if err != nil {
		logging.L().Error("Repository Create: Failed to insert user",
			"email", user.Email,
			"error", err)
		return fmt.Errorf("failed to create user: %w", err)
	}

	logging.L().Debug("Repository Create: User created successfully",
		"email", user.Email,
		"userID", user.ID,
		"passwordHashLength", len(user.PasswordHash))

	// Immediately verify the user was saved correctly by reading it back
	verifyUser, err := r.FindByID(context.Background(), user.ID)
	if err != nil {
		logging.L().Error("Repository Create: Failed to verify user creation",
			"email", user.Email,
			"userID", user.ID,
			"error", err)
	} else {
		logging.L().Debug("Repository Create: Verification read successful",
			"email", verifyUser.Email,
			"userID", verifyUser.ID,
			"verifyPasswordHashLength", len(verifyUser.PasswordHash),
			"verifyPasswordHashEmpty", verifyUser.PasswordHash == "")
	}

	return nil
}

// Update implements UserRepository.Update.
// Updates an existing user record in the database.
//
// Parameters:
//   - ctx: Context for managing timeouts and cancellation
//   - user: The user model with updated fields
//
// Returns:
//   - error: Any error encountered during the operation
func (r *Repository) Update(ctx context.Context, user *models.User) error {
	// Prepare user for update
	if err := user.BeforeUpdate(); err != nil {
		return fmt.Errorf("failed to prepare user for update: %w", err)
	}

	logging.L().Debug("Repository Update: Updating user",
		"email", user.Email,
		"userID", user.ID,
		"passwordHashLength", len(user.PasswordHash),
		"passwordHashEmpty", user.PasswordHash == "")

	// Update user - Align with migration 0002 schema
	query := `
		UPDATE users
		SET email = $1, first_name = $2, last_name = $3, role = $4, password_hash = $5,
		    is_active = $6, is_subscribed = $7, subscription_status = $8,
		    trial_start_date = $9, trial_end_date = $10,
		    subscription_start = $11, subscription_end = $12,
		    last_login = $13, updated_at = $14
		WHERE id = $15
	`

	_, err := r.db.Pool.Exec(
		ctx, query,
		user.Email, user.FirstName, user.LastName, user.Role, user.PasswordHash,
		user.IsActive, user.IsSubscribed, user.SubscriptionStatus,
		user.TrialStartDate, user.TrialEnd, // Map to trial_start_date and trial_end_date
		user.SubscriptionStart, user.SubscriptionEnd,
		user.LastLogin, user.UpdatedAt, user.ID,
	)

	if err != nil {
		logging.L().Error("Repository Update: Failed to update user",
			"email", user.Email,
			"userID", user.ID,
			"error", err)
		return fmt.Errorf("failed to update user: %w", err)
	}

	logging.L().Debug("Repository Update: User updated successfully",
		"email", user.Email,
		"userID", user.ID,
		"passwordHashLength", len(user.PasswordHash))

	return nil
}

// Delete implements UserRepository.Delete.
// Deletes a user from the database by ID.
//
// Parameters:
//   - ctx: Context for managing timeouts and cancellation
//   - id: The ID of the user to delete
//
// Returns:
//   - error: Any error encountered during the operation
func (r *Repository) Delete(ctx context.Context, id int64) error {
	query := "DELETE FROM users WHERE id = $1"
	_, err := r.db.Pool.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}
	return nil
}

// Count is a helper method to count users matching a filter
func (r *Repository) Count(ctx context.Context, filter map[string]interface{}) (int64, error) {
	// Build query
	baseQuery := "SELECT COUNT(*) FROM users"

	// Add WHERE clause for filters
	whereClause := make([]string, 0)
	args := make([]interface{}, 0)
	argIndex := 1

	for key, value := range filter {
		whereClause = append(whereClause, fmt.Sprintf("%s = $%d", key, argIndex))
		args = append(args, value)
		argIndex++
	}

	query := baseQuery
	if len(whereClause) > 0 {
		query += " WHERE " + strings.Join(whereClause, " AND ")
	}

	// Execute query
	var count int64
	if err := r.db.Pool.QueryRow(ctx, query, args...).Scan(&count); err != nil {
		return 0, fmt.Errorf("failed to count users: %w", err)
	}

	return count, nil
}

// FindByID implements UserRepository.FindByID.
// Retrieves a user by their ID.
//
// Parameters:
//   - ctx: Context for managing timeouts and cancellation
//   - id: The ID of the user to find
//
// Returns:
//   - *models.User: The found user or nil if not found
//   - error: Any error encountered during the operation
func (r *Repository) FindByID(ctx context.Context, id int64) (*models.User, error) {
	query := `
		SELECT id, email, first_name, last_name, password_hash, role, is_active, is_subscribed,
		       subscription_status, trial_start_date, trial_end_date, subscription_start, subscription_end,
		       last_login, created_at, updated_at
		FROM users
		WHERE id = $1
	`

	user, err := r.scanRow(r.db.Pool.QueryRow(ctx, query, id))
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, repository.ErrUserNotFound
		}
		return nil, fmt.Errorf("failed to find user by ID: %w", err)
	}

	if user == nil {
		return nil, repository.ErrUserNotFound
	}

	// Load related data
	if err := r.loadUserRelatedData(ctx, user); err != nil {
		return nil, err
	}

	return user, nil
}

// FindByEmail implements UserRepository.FindByEmail.
// Retrieves a user by their email address.
//
// Parameters:
//   - ctx: Context for managing timeouts and cancellation
//   - email: The email address of the user to find
//
// Returns:
//   - *models.User: The found user or nil if not found
//   - error: Any error encountered during the operation
func (r *Repository) FindByEmail(ctx context.Context, email string) (*models.User, error) {
	query := `
		SELECT id, email, first_name, last_name, password_hash, role, is_active, is_subscribed,
		       subscription_status, trial_start_date, trial_end_date, subscription_start, subscription_end,
		       last_login, created_at, updated_at
		FROM users
		WHERE email = $1
	`

	user, err := r.scanRow(r.db.Pool.QueryRow(ctx, query, email))
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, repository.ErrUserNotFound
		}
		return nil, fmt.Errorf("failed to find user by email: %w", err)
	}

	if user == nil {
		return nil, repository.ErrUserNotFound
	}

	// Load related data
	if err := r.loadUserRelatedData(ctx, user); err != nil {
		// Log the error but don't fail the authentication - related data is not critical
		logging.L().Warn("Failed to load user related data", "userID", user.ID, "error", err)
		// Continue without related data
	}

	return user, nil
}

// scanRow implements UserRepository.scanRow.
// Scans a database row into a user model.
//
// Parameters:
//   - row: The database row to scan
//
// Returns:
//   - *models.User: The scanned user model
//   - error: Any error encountered during scanning
func (r *Repository) scanRow(row pgx.Row) (*models.User, error) {
	var u models.User
	err := row.Scan(
		&u.ID,
		&u.Email,
		&u.FirstName,
		&u.LastName,
		&u.PasswordHash,
		&u.Role,
		&u.IsActive,
		&u.IsSubscribed,
		&u.SubscriptionStatus,
		&u.TrialStartDate,
		&u.TrialEnd,
		&u.SubscriptionStart,
		&u.SubscriptionEnd,
		&u.LastLogin,
		&u.CreatedAt,
		&u.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, repository.ErrUserNotFound
		}
		return nil, fmt.Errorf("failed to scan user row: %w", err)
	}

	return &u, nil
}

// UpdateLastLogin updates only the last_login timestamp for a user.
// This method is more efficient and safer than updating the entire user record.
//
// Parameters:
//   - ctx: Context for managing timeouts and cancellation
//   - userID: The ID of the user whose last login should be updated
//   - lastLogin: The timestamp to set as the last login time
//
// Returns:
//   - error: Any error encountered during the operation
func (r *Repository) UpdateLastLogin(ctx context.Context, userID int64, lastLogin time.Time) error {
	query := `UPDATE users SET last_login = $1, updated_at = $2 WHERE id = $3`

	_, err := r.db.Pool.Exec(ctx, query, lastLogin, time.Now(), userID)
	if err != nil {
		return fmt.Errorf("failed to update last login: %w", err)
	}

	logging.L().Debug("Repository UpdateLastLogin: Last login updated successfully",
		"userID", userID,
		"lastLogin", lastLogin)

	return nil
}

// UpsertRoles implements UserRepository.UpsertRoles.
// Updates or inserts user role associations.
//
// Parameters:
//   - ctx: Context for managing timeouts and cancellation
//   - userID: The ID of the user whose roles are being updated
//   - roles: Slice of roles to associate with the user
//
// Returns:
//   - error: Any error encountered during the operation
func (r *Repository) UpsertRoles(ctx context.Context, userID string, roles []string) error {
	// Implementation would go here
	return nil
}

// UpsertClaims implements UserRepository.UpsertClaims.
// Updates or inserts user claim associations.
//
// Parameters:
//   - ctx: Context for managing timeouts and cancellation
//   - userID: The ID of the user whose claims are being updated
//   - claims: Slice of claims to associate with the user
//
// Returns:
//   - error: Any error encountered during the operation
func (r *Repository) UpsertClaims(ctx context.Context, userID string, claims []map[string]interface{}) error {
	// This is a placeholder. Implement actual logic based on your requirements.
	logging.L().Info("Upserting claims for user", "user_id", userID, "claims_count", len(claims))
	return nil
}

// SearchUsers implements UserRepository.SearchUsers.
// Searches for users based on the provided query string, checking against
// username, email, and other searchable user fields.
//
// Parameters:
//   - ctx: Context for managing timeouts and cancellation
//   - search: Search query to match against user fields
//   - limit: Maximum number of results to return
//
// Returns:
//   - []models.User: Slice of users matching the search criteria
//   - error: Any error encountered during the operation
func (r *Repository) SearchUsers(ctx context.Context, search string, limit int) ([]models.User, error) {
	// This is a placeholder. Implement actual logic based on your requirements.
	logging.L().Info("Searching users", "search_string", search, "limit", limit)
	return []models.User{}, nil
}

// FindUsersByIDs implements UserRepository.FindUsersByIDs.
// Retrieves multiple users by their IDs in a single efficient database query.
//
// Parameters:
//   - ctx: Context for managing timeouts and cancellation
//   - ids: Slice of user IDs to find
//
// Returns:
//   - []models.User: Slice of users matching the provided IDs
//   - error: Any error encountered during the operation
func (r *Repository) FindUsersByIDs(ctx context.Context, ids []string) ([]models.User, error) {
	// This is a placeholder. Implement actual logic based on your requirements.
	logging.L().Info("Finding users by IDs", "ids_count", len(ids))
	return []models.User{}, nil
}
