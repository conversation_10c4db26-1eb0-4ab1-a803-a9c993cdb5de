// Package user provides user management functionality.
//
// This package handles operations related to user accounts, including
// authentication, profile management, and user-specific settings.
package user

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/coolbox13/server_go/internal/cache"
	"github.com/coolbox13/server_go/internal/config"
	"github.com/coolbox13/server_go/internal/logging"
	"github.com/coolbox13/server_go/internal/models"
	"github.com/coolbox13/server_go/internal/repository"
	"github.com/coolbox13/server_go/internal/servicecore"
	"github.com/coolbox13/server_go/internal/utils"
)

// Error message constants for user service operations
const (
	// errUserGetFailed is used when a user retrieval operation fails
	errUserGetFailed = "failed to get user: %w"
	// errUserNotFoundMsg is used when a user is not found
	errUserNotFoundMsg = "user not found"
	// errUserCacheFailed is used when caching a user fails
	errUserCacheFailed = "Failed to cache user: %v\n"
	// errUserClearCacheFailed is used when clearing a user cache fails
	errUserClearCacheFailed  = "Failed to clear user cache: %v\n"
	errUsernameExists        = "username already exists"
	errEmailExists           = "email already exists"
	errPasswordHashingFailed = "failed to hash password: %w"
	errInvalidCredentials    = "invalid email/username or password"
)

// Service handles user-related operations including management of user data,
// user preferences, favorites, and shopping lists.
//
// Service implements caching for user data to improve performance and
// reduce database load for frequently accessed user information.
type Service struct {
	*servicecore.BaseService
	// UserRepo provides access to user data storage
	UserRepo repository.UserRepository
}

// New creates a new user service with the provided dependencies.
//
// Parameters:
//   - cfg: Application configuration
//   - cache: Cache implementation for caching user data
//   - userRepo: Repository for user data persistence
//
// Returns:
//   - *Service: A configured user service ready for use
func New(
	cfg *config.Config,
	cache *cache.Cache,
	userRepo repository.UserRepository,
) *Service {
	return &Service{
		BaseService: servicecore.NewBaseService(cfg, cache),
		UserRepo:    userRepo,
	}
}

// GetUserByID retrieves a user by their unique identifier.
//
// This method implements caching, first attempting to retrieve the user from cache
// before falling back to the database. Successfully retrieved users are cached
// for future requests.
//
// Parameters:
//   - ctx: Context for managing operation timeouts and cancellation
//   - id: The unique identifier of the user to retrieve
//
// Returns:
//   - *models.User: The retrieved user, or nil if not found
//   - error: Any error encountered during the retrieval operation
func (s *Service) GetUserByID(ctx context.Context, id int64) (*models.User, error) {
	// Use standardized key builder
	cacheKey := cache.BuildUserKey(fmt.Sprintf("%d", id))

	// Try to get from cache first
	var user models.User
	found, err := s.Cache.Get(ctx, "users", cacheKey, &user)
	if err == nil && found {
		return &user, nil
	}

	// If not in cache, get from database
	userPtr, err := s.UserRepo.FindByID(ctx, id)
	if err != nil {
		if errors.Is(err, repository.ErrUserNotFound) {
			return nil, errors.New(errUserNotFoundMsg) // Return a service-level not found message or error
		}
		return nil, fmt.Errorf(errUserGetFailed, err) // For other errors
	}

	// If err is nil, userPtr should not be nil based on repository contract
	if userPtr == nil {
		// This case should ideally not be reached if repository.ErrUserNotFound is returned correctly.
		// However, as a safeguard:
		return nil, errors.New(errUserNotFoundMsg)
	}

	// Store in cache for future requests
	if err := s.Cache.Set(ctx, "users", cacheKey, *userPtr, cache.TTLMedium); err != nil {
		// Log the error but don't fail the request
		logging.Warn("Failed to cache user",
			"user_id", userPtr.ID,
			"error", err.Error(),
		)
	}

	return userPtr, nil
}

// GetUserByEmail retrieves a user by their email address.
//
// This method implements caching, first attempting to retrieve the user from cache
// before falling back to the database. Successfully retrieved users are cached
// for future requests.
//
// Parameters:
//   - ctx: Context for managing operation timeouts and cancellation
//   - email: The email address of the user to retrieve
//
// Returns:
//   - *models.User: The retrieved user, or nil if not found
//   - error: Any error encountered during the retrieval operation
func (s *Service) GetUserByEmail(ctx context.Context, email string) (*models.User, error) {
	// Use standardized key builder
	cacheKey := cache.BuildUserEmailKey(email)

	// Try to get from cache first
	var user models.User
	found, err := s.Cache.Get(ctx, "users", cacheKey, &user)
	if err == nil && found {
		return &user, nil
	}

	// If not in cache, get from database
	userPtr, err := s.UserRepo.FindByEmail(ctx, email)
	if err != nil {
		if errors.Is(err, repository.ErrUserNotFound) {
			return nil, errors.New(errUserNotFoundMsg) // Return a service-level not found message or error
		}
		return nil, fmt.Errorf(errUserGetFailed, err) // For other errors
	}

	// If err is nil, userPtr should not be nil based on repository contract
	if userPtr == nil {
		// This case should ideally not be reached if repository.ErrUserNotFound is returned correctly.
		// However, as a safeguard:
		return nil, errors.New(errUserNotFoundMsg)
	}

	// Store in cache for future requests
	if err := s.Cache.Set(ctx, "users", cacheKey, *userPtr, cache.TTLMedium); err != nil {
		// Log the error but don't fail the request
		logging.Warn("Failed to cache user",
			"user_id", userPtr.ID,
			"email", userPtr.Email,
			"error", err.Error(),
		)
	}

	return userPtr, nil
}

// CreateUser creates a new user in the system.
//
// Parameters:
//   - ctx: Context for managing operation timeouts and cancellation
//   - user: The user model containing the information to persist
//
// Returns:
//   - error: Any error encountered during the creation operation
func (s *Service) CreateUser(ctx context.Context, user *models.User) error {
	// Create user
	if err := s.UserRepo.Create(ctx, user); err != nil {
		return fmt.Errorf("failed to create user: %w", err)
	}

	return nil
}

// RegisterUser handles the registration of a new user.
// It validates input, checks for existing email, hashes the password,
// and then creates the user via the repository.
func (s *Service) RegisterUser(ctx context.Context, email, plainPassword string) (*models.User, error) {
	// Validate input (basic example, expand as needed)
	if email == "" || plainPassword == "" {
		return nil, errors.New("email and password cannot be empty")
	}

	// Check if email already exists
	existingUserByEmail, _ := s.UserRepo.FindByEmail(ctx, email)
	if existingUserByEmail != nil {
		return nil, errors.New(errEmailExists)
	}

	// Hash the password
	hashedPassword, err := utils.HashPassword(plainPassword)
	if err != nil {
		return nil, fmt.Errorf(errPasswordHashingFailed, err)
	}

	logging.L().Debug("RegisterUser: Password hashed successfully",
		"email", email,
		"hashedPasswordLength", len(hashedPassword),
		"hashedPasswordPrefix", hashedPassword[:min(10, len(hashedPassword))])

	// Create the user model
	newUser := &models.User{
		Email:        email,
		FirstName:    "", // Will be filled in later via profile update
		LastName:     "", // Will be filled in later via profile update
		PasswordHash: hashedPassword,
		IsActive:     true,   // Default to active, or implement activation flow
		Role:         "user", // Default role
		// Initialize other fields as necessary, e.g., CreatedAt, UpdatedAt via BeforeCreate hook
	}

	logging.L().Debug("RegisterUser: User model created",
		"email", newUser.Email,
		"passwordHashLength", len(newUser.PasswordHash),
		"role", newUser.Role)

	// Call repository to create the user
	if err := s.UserRepo.Create(ctx, newUser); err != nil {
		// Note: CreateUser in the service currently just calls repo.Create.
		// If CreateUser had more logic, we might call that instead, but it needs newUser.ID for caching.
		// For now, directly calling repo.Create is fine, then we get the ID for potential caching if needed.
		return nil, fmt.Errorf("failed to create user in repository: %w", err)
	}

	return newUser, nil
}

// AuthenticateUser verifies a user's credentials using email and password.
// It finds the user by email and checks the password hash.
func (s *Service) AuthenticateUser(ctx context.Context, email, password string) (*models.User, error) {
	// Find user by email
	user, err := s.UserRepo.FindByEmail(ctx, email)
	if err != nil {
		if errors.Is(err, repository.ErrUserNotFound) {
			logging.L().Debug("AuthenticateUser: User not found", "email", email)
			return nil, errors.New(errInvalidCredentials) // Email not found
		}
		// A different error occurred when finding by email
		logging.L().Error("AuthenticateUser: Error finding user", "email", email, "error", err)
		return nil, fmt.Errorf("failed to find user by email: %w", err)
	}

	// If user is nil at this point (e.g. if FindByEmail returned nil, nil which it shouldn't anymore)
	if user == nil {
		logging.L().Debug("AuthenticateUser: User is nil", "email", email)
		return nil, errors.New(errInvalidCredentials)
	}

	logging.L().Debug("AuthenticateUser: User found",
		"email", email,
		"userID", user.ID,
		"passwordHashLength", len(user.PasswordHash),
		"passwordHashEmpty", user.PasswordHash == "")

	// Check password
	if !utils.CheckPasswordHash(password, user.PasswordHash) {
		logging.L().Debug("AuthenticateUser: Password check failed",
			"email", email,
			"providedPasswordLength", len(password),
			"storedHashLength", len(user.PasswordHash))
		return nil, errors.New(errInvalidCredentials) // Invalid password
	}

	logging.L().Debug("AuthenticateUser: Password check successful", "email", email)

	return user, nil
}

// UpdateUser updates an existing user's information.
//
// After successfully updating the user in the database, this method
// also invalidates any related cache entries to ensure data consistency.
//
// Parameters:
//   - ctx: Context for managing operation timeouts and cancellation
//   - user: The user model containing the updated information
//
// Returns:
//   - error: Any error encountered during the update operation
func (s *Service) UpdateUser(ctx context.Context, user *models.User) error {
	// Update user
	if err := s.UserRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("failed to update user: %w", err)
	}

	// Clear user cache using standardized key builder
	cacheKey := cache.BuildUserKey(fmt.Sprintf("%d", user.ID))
	if err := s.Cache.Delete(ctx, "users", cacheKey); err != nil {
		// Log the error but don't fail the request
		logging.Warn("Failed to clear user cache",
			"user_id", user.ID,
			"error", err.Error(),
		)
	}

	// Also clear email cache if email is present
	if user.Email != "" {
		emailCacheKey := cache.BuildUserEmailKey(user.Email)
		if err := s.Cache.Delete(ctx, "users", emailCacheKey); err != nil {
			// Log the error but don't fail the request
			logging.Warn("Failed to clear user email cache",
				"user_id", user.ID,
				"email", user.Email,
				"error", err.Error(),
			)
		}
	}

	return nil
}

// UpdateLastLogin updates a user's last login timestamp to the current time.
//
// This method retrieves the user, updates the LastLogin field, and persists
// the change back to the database.
//
// Parameters:
//   - ctx: Context for managing operation timeouts and cancellation
//   - userID: The unique identifier of the user to update
//
// Returns:
//   - error: Any error encountered during the update operation
func (s *Service) UpdateLastLogin(ctx context.Context, userID int64) error {
	logging.L().Debug("UpdateLastLogin: Starting", "userID", userID)

	// Use the new repository method that only updates the last_login field
	// This avoids the issue of accidentally overwriting the password hash
	lastLogin := time.Now()

	// Check if the repository has the UpdateLastLogin method
	if repo, ok := s.UserRepo.(interface {
		UpdateLastLogin(ctx context.Context, userID int64, lastLogin time.Time) error
	}); ok {
		logging.L().Debug("UpdateLastLogin: Using repository UpdateLastLogin method", "userID", userID)
		err := repo.UpdateLastLogin(ctx, userID, lastLogin)
		if err != nil {
			logging.L().Error("UpdateLastLogin: Failed to update last login", "userID", userID, "error", err)
		} else {
			logging.L().Debug("UpdateLastLogin: Last login updated successfully", "userID", userID)
		}
		return err
	}

	// Fallback to the old method if the repository doesn't support the new method
	logging.L().Warn("UpdateLastLogin: Repository doesn't support UpdateLastLogin method, falling back to full user update", "userID", userID)

	// Get user
	user, err := s.GetUserByID(ctx, userID)
	if err != nil {
		logging.L().Error("UpdateLastLogin: Failed to get user", "userID", userID, "error", err)
		return fmt.Errorf(errUserGetFailed, err)
	}

	logging.L().Debug("UpdateLastLogin: User retrieved",
		"userID", userID,
		"email", user.Email,
		"passwordHashLength", len(user.PasswordHash),
		"passwordHashEmpty", user.PasswordHash == "")

	// Update last login
	user.LastLogin = lastLogin

	logging.L().Debug("UpdateLastLogin: About to update user",
		"userID", userID,
		"passwordHashLength", len(user.PasswordHash),
		"passwordHashEmpty", user.PasswordHash == "")

	// Update user
	err = s.UpdateUser(ctx, user)
	if err != nil {
		logging.L().Error("UpdateLastLogin: Failed to update user", "userID", userID, "error", err)
	} else {
		logging.L().Debug("UpdateLastLogin: User updated successfully", "userID", userID)
	}
	return err
}
