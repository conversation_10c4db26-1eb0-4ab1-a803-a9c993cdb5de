{{define "login-content"}}
<div class="auth-page">
    <div class="auth-container">
        <div class="auth-header">
            <h1>Welcome Back</h1>
            <p>Sign in to your Omfietser account</p>
        </div>

        <div class="auth-card">
        {{if .Flashes}}
            <div id="error-messages" class="auth-error-messages">
                {{range .Flashes}}
                    <p>{{.}}</p>
                {{end}}
            </div>
        {{else}}
            <div id="error-messages"></div>
        {{end}}

        <form hx-post="/login" hx-target="#error-messages" hx-swap="outerHTML" class="auth-form">
            <input type="hidden" name="csrf_token" value="{{.CSRFToken}}">

            <div class="auth-form-group">
                <label for="email" class="auth-form-label">Email Address</label>
                <input type="email" id="email" name="email" value="{{.FormEmail}}"
                       class="auth-form-input"
                       placeholder="<EMAIL>"
                       autocomplete="email"
                       required>
            </div>

            <div class="auth-form-group">
                <label for="password" class="auth-form-label">Password</label>
                <input type="password" id="password" name="password"
                       class="auth-form-input"
                       placeholder="Enter your password"
                       autocomplete="current-password"
                       required>
            </div>

            <div class="auth-form-submit">
                <button type="submit" class="auth-btn">
                    Sign In
                </button>
            </div>
        </form>
        </div>

        <div class="auth-footer">
            <p>Don't have an account?
                <a href="/?auth=signup"
                   hx-get="/?auth=signup"
                   hx-target="main"
                   hx-push-url="true"
                   hx-swap="innerHTML">
                    Create one here
                </a>
            </p>
        </div>
    </div>
</div>

<script>
    // Add loading indicator for form submission
    document.addEventListener('htmx:beforeRequest', function(event) {
        if (event.detail.elt.tagName === 'FORM') {
            const button = event.detail.elt.querySelector('button[type="submit"]');
            if (button) {
                button.innerHTML = '<span class="auth-spinner"></span> Logging in...';
                button.disabled = true;
            }
        }
    });

    document.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.elt.tagName === 'FORM') {
            const button = event.detail.elt.querySelector('button[type="submit"]');
            if (button) {
                button.innerHTML = 'Sign In';
                button.disabled = false;
            }
        }
    });
</script>
{{end}}
