{{ define "header" }}
<header class="header">
    <div class="header__container">
        <a href="/" class="header__logo">
            <img src="{{ AssetPath "images/bike.svg" }}" alt="Omfietser" class="header__logo-icon" hx-preserve="true">
        </a>

        <div class="header__actions">
            <div class="header__search">
                <form action="/search" method="GET" class="header__search-form"
                    id="header-search-form"
                    hx-get="/products"
                    hx-target="#product-list-container"
                    hx-swap="innerHTML"
                    hx-push-url="true"
                    hx-trigger="submit"
                    hx-include="#filter-sidebar-form, #header-search-form input[name='q']"
                    >
                    <input
                        type="text"
                        class="header__search-input"
                        placeholder="Zoek producten..."
                        hx-get="/search/suggestions"
                        hx-trigger="keyup changed delay:300ms"
                        hx-target="#search-results"
                        hx-swap="innerHTML"
                        name="q"
                        autocomplete="off"
                    >
                    <!-- Hidden CSRF token for HTMX requests -->
                    <input type="hidden" name="csrf_token" value="{{ .CSRFToken }}">
                </form>
                <div id="search-results" class="search-results"></div>
            </div>

            <!-- Sort by price high to low: Target is now #product-list (the parent) -->
            <a href="#" class="header__icon-button"
               hx-get="/products?sort=current_price DESC NULLS LAST"
               hx-target="#product-list-container"
               hx-swap="innerHTML"
               hx-push-url="true"
               aria-label="Sort by Price High to Low">
                <img src="{{ AssetPath "images/cart-icon.svg" }}" alt="Sort by Price High to Low" hx-preserve="true">
            </a>

            <!-- User Authentication Menu -->
            {{ if .IsLoggedIn }}
                <!-- User Menu for logged in users -->
                <div class="header__user-menu">
                    <button class="header__icon-button header__user-button header__user-button--logged-in"
                            onclick="toggleUserMenu()"
                            aria-label="User Menu">
                        <img src="{{ AssetPath "images/user-icon.svg" }}" alt="User Menu" hx-preserve="true">
                    </button>
                    <div class="header__user-dropdown" id="user-dropdown">
                        {{ if ($.Template.Lookup "user-menu") }}
                            {{ template "user-menu" . }}
                        {{ else }}
                            <div class="user-menu">
                                <div class="user-menu__logged-in">
                                    <div class="user-menu__info">
                                        <span class="user-menu__username">{{ .User.Username }}</span>
                                        <span class="user-menu__email">{{ .User.Email }}</span>
                                    </div>
                                    <div class="user-menu__actions">
                                        <a href="/profile" class="user-menu__link">Profile</a>
                                        <a href="/settings" class="user-menu__link">Settings</a>
                                        <form action="/logout" method="post" class="user-menu__logout-form">
                                            <input type="hidden" name="csrf_token" value="{{ .CSRFToken }}">
                                            <button type="submit" class="user-menu__logout-button">Logout</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        {{ end }}
                    </div>
                </div>
            {{ else }}
                <!-- Authentication menu for non-authenticated users -->
                <div class="header__user-menu">
                    <button class="header__icon-button header__user-button header__user-button--logged-out"
                            onclick="toggleUserMenu()"
                            aria-label="User Authentication">
                        <img src="{{ AssetPath "images/user-icon.svg" }}" alt="User Authentication" hx-preserve="true">
                    </button>
                    <div class="header__user-dropdown" id="user-dropdown">
                        <div class="user-menu">
                            <div class="user-menu__not-logged-in">
                                <div class="user-menu__actions">
                                    <a href="/?auth=login"
                                       hx-get="/?auth=login"
                                       hx-target="main"
                                       hx-swap="innerHTML"
                                       hx-push-url="true"
                                       class="user-menu__link">Inloggen</a>
                                    <a href="/?auth=signup"
                                       hx-get="/?auth=signup"
                                       hx-target="main"
                                       hx-swap="innerHTML"
                                       hx-push-url="true"
                                       class="user-menu__link">Registreren</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {{ end }}

            <!-- Information Page Navigation -->
            <button class="header__icon-button"
                    hx-get="/"
                    hx-target="main"
                    hx-swap="innerHTML"
                    aria-label="Information">
                <img src="{{ AssetPath "images/info-icon.svg" }}" alt="Information" hx-preserve="true">
            </button>

            <button class="header__theme-toggle theme-toggle" aria-label="Toggle theme" data-action="toggle-theme">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="theme-toggle__icon">
                    <!-- Sun icon (shows in dark mode) -->
                    <g class="sun-icon">
                        <circle cx="12" cy="12" r="5"></circle>
                        <line x1="12" y1="1" x2="12" y2="3"></line>
                        <line x1="12" y1="21" x2="12" y2="23"></line>
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                        <line x1="1" y1="12" x2="3" y2="12"></line>
                        <line x1="21" y1="12" x2="23" y2="12"></line>
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                    </g>
                </svg>
            </button>
        </div>
    </div>

    <!-- Filter Chips Area -->
    <div class="filter-chips-container">
        {{ if and .FilterState .FilterState.HasFilters }}
            {{/* Try to use the filter-chips template, but fall back to inline content if it fails */}}
            {{ if ($.Template.Lookup "filter-chips") }}
                {{ template "filter-chips" . }}
            {{ else }}
                <div class="filter-chips" id="active-filters">
                    <div class="filter-chips__container">
                        {{ range $key, $values := .FilterState.Filters }}
                            {{ range $values }}
                                <div class="filter-chip">
                                    <span class="filter-chip__label">
                                        <span class="filter-chip__type">{{ formatFilterType $key }}</span>
                                        <span class="filter-chip__value">{{ formatFilterValue $key . }}</span>
                                    </span>
                                    <button
                                        class="filter-chip__remove"
                                        hx-delete="/filters/remove/{{ $key }}/{{ . }}"
                                        hx-target="#product-list-container"
                                        hx-swap="innerHTML"
                                        hx-indicator=".filter-loading"
                                        aria-label="Remove filter"
                                    >×</button>
                                </div>
                            {{ end }}
                        {{ end }}
                    </div>
                </div>
            {{ end }}
        {{ else }}
            <div class="filter-chips filter-chips--empty" id="active-filters">
                <div class="filter-chip filter-chip--info">
                    <span class="filter-chip__label">Geen filters toegepast</span>
                </div>
            </div>
        {{ end }}
    </div>
</header>


{{ end }}