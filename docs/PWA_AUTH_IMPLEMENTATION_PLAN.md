# PWA Authentication Implementation Plan

**Date**: May 29, 2025
**Goal**: Block direct URL access and create true PWA/app experience where authentication is content that swaps into main area only through app navigation.

## **Current Status**
- ✅ Hybrid template architecture implemented
- ✅ Authentication pages render with full base layout
- ✅ HTMX navigation works in header user menu
- ❌ Direct URL access still possible (needs blocking)
- ❌ Still feels like website, not PWA/app

## **User Requirements**
> "I do not see the need for this direct approach of loading pages. So it should be more or less impossible. I want a PWA/app like experience, not some website where you can go to a URL direct."

**Key Insight**: Authentication should be app state, not separate pages.

---

## **Phase 1: Block Direct URL Access**
**Objective**: Make `/login` and `/signup` redirect to `/` and trigger authentication UI

### **Implementation Steps:**
1. **Modify AuthHandler routes**:
   - `/login` GET → redirect to `/?auth=login`
   - `/signup` GET → redirect to `/?auth=signup`
   - Keep POST routes for form submission

2. **Update main page handler**:
   - Check for `?auth=login` or `?auth=signup` query params
   - If present, render base layout with authentication content
   - Set appropriate page state/data

3. **Test direct access**:
   - `curl http://localhost:8088/login` → should redirect to `/?auth=login`
   - Browser should show main app with login content

---

## **Phase 2: App-State Authentication**
**Objective**: Authentication becomes app state, not separate pages

### **Implementation Steps:**
1. **Update header navigation**:
   - Change user menu links from `/login` to `/?auth=login`
   - Use HTMX to swap main content area
   - Maintain app layout throughout

2. **Modify base template logic**:
   - Check for auth query params
   - Render appropriate authentication content in main area
   - Keep header/footer persistent

3. **Update authentication content templates**:
   - Ensure forms work within main content area
   - Update form targets and redirects
   - Test HTMX form submission

---

## **Phase 3: URL State Management**
**Objective**: Clean URL handling for app-like experience

### **Implementation Steps:**
1. **URL rewriting**:
   - Use HTMX `hx-push-url` to update browser URL
   - Show clean URLs like `/?auth=login` during authentication
   - Return to `/` after successful login/signup

2. **Browser history**:
   - Ensure back button works correctly within app
   - Prevent breaking out of app experience
   - Handle browser refresh gracefully

3. **Route protection**:
   - Block any other direct authentication routes
   - Ensure all auth flows go through main app

---

## **Phase 4: Testing & Validation**
**Objective**: Verify true PWA/app experience

### **Test Cases:**
1. **Direct URL blocking**:
   - ✅ `/login` → redirects to `/?auth=login`
   - ✅ `/signup` → redirects to `/?auth=signup`
   - ✅ No full page reloads during auth flow

2. **App navigation**:
   - ✅ User menu → login content swaps in main area
   - ✅ Header/footer remain persistent
   - ✅ Forms submit and handle responses correctly

3. **PWA behavior**:
   - ✅ Feels like app, not website
   - ✅ No jarring page transitions
   - ✅ Consistent layout throughout

---

## **Expected Outcome**
- **Direct URLs blocked**: `/login` impossible to access directly
- **App-first experience**: All authentication happens within main app layout
- **Content swapping**: Login/signup content appears in main area via HTMX
- **True PWA feel**: No traditional website navigation patterns

---

## **Files to Modify**
1. `internal/handlers/auth_handler.go` - Add redirects for GET requests
2. `internal/handlers/frontend_handler.go` - Handle auth query params
3. `frontend/templates/partials/header.html` - Update navigation links
4. `frontend/templates/layouts/base.html` - Add auth state logic
5. `template issues 290525.md` - Document implementation progress

---

## **Architecture Comparison**

### **Before (Website Pattern)**
```
/login → Full login page with header/footer
/signup → Full signup page with header/footer
Direct URL access possible
```

### **After (PWA Pattern)**
```
/login → Redirect to /?auth=login
/?auth=login → Main app with login content in main area
Direct URL access blocked
```

**Ready to implement!** 🚀
