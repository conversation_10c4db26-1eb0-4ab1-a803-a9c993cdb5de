### UI Components Functionality

#### Header Components

- **Logo**: Returns to home page, with default filter and sort, when logged in. All promotion products and sorted descending on absolute discount. The home page is not the home.html file. If a user is not logged in the information page (which is called home.html) should be shown. The user can then choose to login or signup. and have some explanantion about the app. No need to change anything to home.html now.
- **Search Bar**: Real-time suggestions with HTMX (keyup triggered). The result should be a product list with the search item. Current filter and sort should be applied to the search result. 
- **Cart <PERSON><PERSON>**: Shopping cart management, provission in the backend and db are allready present. Similar to favorites, but do have a more temporarily character. It functions as the shoping card for this weeks promotions and grocery shopping. It needs a special view in main. To be developed as a separate task. To start it, just show the products in the cart, which are selected in the product cards, completely the same as favorites.
- **Account But<PERSON>**: When NOT logged in: Clicking auth icon should navigate to a full-page authentication interface (login/signup in main viewport)
   - When logged in: Clicking auth icon should navigate to a user management page with:
     - User preferences section (placeholder for now - just show "User Preferences - Coming Soon")
     - Logout functionality
     - Profile information display
- **Information button**: Shows the home page (home.html), later to become the default when opening the app if someone is not logged in. Uses the information icon
- **Theme Toggle**: Light/dark mode switch. Implemented
- **Filter Chips Area**: Shows active filters with removal capability, not working properly yet so still to be improved

#### Footer Navigation

- **Filter Button**: Opens the sidebar. Filtering and sorting options. Currently working.
- **Favorites Button**: Saved product list access, provission in the backend and db are allready present, a user can create it's own filter for favorite products by adding them to the favorites list. The user can do this by selecting the heart icon on a product card. The favorites button (heart icon) in the header selects the filter and shows the filtered product list. Favorites are saved for the inidvidual user
- **Savings (€) Button**: Sorts by absolute discount. The sort should always be applied to the current product list in view, which could be a filtered one, or a search result.
- **Savings (%) Button**: Sorts by percentage discount. The sort should always be applied to the current product list in view, which could be a filtered one, or a search result. 

#### Product Card Actions

- **Shop Logo**: Currently non-functional (potential future shop filter trigger, keep it like this)
- **Favorite Button**: Adds/removes from user favorites
- **Add to Cart Button**: Adds or removes from the shopping cart
- **Info Button**: Replaces product list with detailed single product view showing:
    - Price history data
    - Comparable products with their cards
For now show a placeholder card telling the user that the feature is to be implemented. It would be nice if the product card turns around, animation.
- **Promotion Badge**: Informational only, not interactive. Part of the product card styling. Is to be developed. NOt an immediate task, due to, still, faulthy data.
- **Price Display**: Informational only, not interactive. Part of the product card styling. Is to be developed. NOt an immediate task, due to, still, faulthy data.

### Default Application State

- **Initial filters**: All shops, promotions only, sort  or user preference (to be implemented)
- **Initial sort**: Absolute discount (high to low), or user preference (to be implemented)
- **User preferences**: Override defaults for logged-in users with saved preferences, applicable to filterering and sorting preferences, as described at initial filtering and sorting.